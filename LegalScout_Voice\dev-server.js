/**
 * Development Server for Local API Testing
 * Simulates Vercel's serverless function environment locally
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';

// Load environment variables
dotenv.config();
try {
  dotenv.config({ path: '.env.local' });
} catch (error) {
  console.log('No .env.local file found, using only .env');
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.DEV_API_PORT || 3001;

// Enable CORS
app.use(cors({
  origin: '*',
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'apikey', 'X-Client-Info', 'Prefer', 'Accept'],
  credentials: true
}));

// Parse JSON bodies
app.use(express.json());

// Handle preflight requests
app.options('*', (req, res) => {
  res.status(200).end();
});

// Temporarily disable API handler to fix router error
// TODO: Re-enable once router issue is resolved
console.log('⚠️  API handler temporarily disabled due to router path-to-regexp error');

// Simple fallback API routes for development
app.all('/api/*', (req, res) => {
  console.log(`API request: ${req.method} ${req.url}`);
  res.status(503).json({
    error: 'API temporarily disabled',
    message: 'Development API server is temporarily disabled due to router configuration issue',
    path: req.url,
    method: req.method
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`🚀 Development API server running on http://localhost:${PORT}`);
  console.log(`📡 API endpoints available at http://localhost:${PORT}/api/*`);
  console.log(`🔍 Health check: http://localhost:${PORT}/health`);
});
