<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vapi Comprehensive Test Suite</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .config-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-green { background: #28a745; }
        .status-red { background: #dc3545; }
        .status-yellow { background: #ffc107; }
    </style>
</head>
<body>
    <h1>🎯 Vapi Comprehensive Test Suite</h1>
    <p>This page tests all aspects of Vapi integration in LegalScout Voice.</p>

    <!-- Environment Configuration Test -->
    <div class="test-section">
        <h2><span id="env-status" class="status-indicator status-yellow"></span>Environment Configuration</h2>
        <button onclick="testEnvironment()">Test Environment</button>
        <div id="env-results"></div>
    </div>

    <!-- API Authentication Test -->
    <div class="test-section">
        <h2><span id="auth-status" class="status-indicator status-yellow"></span>API Authentication</h2>
        <button onclick="testAuthentication()">Test Authentication</button>
        <div id="auth-results"></div>
    </div>

    <!-- Assistant Management Test -->
    <div class="test-section">
        <h2><span id="assistant-status" class="status-indicator status-yellow"></span>Assistant Management</h2>
        <button onclick="testAssistants()">Test Assistants</button>
        <div id="assistant-results"></div>
    </div>

    <!-- Voice Configuration Test -->
    <div class="test-section">
        <h2><span id="voice-status" class="status-indicator status-yellow"></span>Voice Configuration</h2>
        <button onclick="testVoiceConfig()">Test Voice Config</button>
        <div id="voice-results"></div>
    </div>

    <!-- Web SDK Test -->
    <div class="test-section">
        <h2><span id="sdk-status" class="status-indicator status-yellow"></span>Web SDK Integration</h2>
        <button onclick="testWebSDK()">Test Web SDK</button>
        <button onclick="startTestCall()" id="start-call-btn">Start Test Call</button>
        <button onclick="stopTestCall()" id="stop-call-btn" disabled>Stop Test Call</button>
        <div id="sdk-results"></div>
    </div>

    <!-- Call Management Test -->
    <div class="test-section">
        <h2><span id="call-status" class="status-indicator status-yellow"></span>Call Management</h2>
        <button onclick="testCallManagement()">Test Call Management</button>
        <div id="call-results"></div>
    </div>

    <!-- Overall Status -->
    <div class="test-section">
        <h2>📊 Overall Test Results</h2>
        <div id="overall-results">
            <p>Run tests to see overall status...</p>
        </div>
        <button onclick="runAllTests()">🚀 Run All Tests</button>
    </div>

    <script>
        // Global variables
        let vapi = null;
        let testResults = {
            environment: null,
            authentication: null,
            assistants: null,
            voice: null,
            sdk: null,
            calls: null
        };

        // Utility functions
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function updateStatus(testName, success) {
            const indicator = document.getElementById(`${testName}-status`);
            indicator.className = `status-indicator ${success ? 'status-green' : 'status-red'}`;
            testResults[testName] = success;
            updateOverallStatus();
        }

        function updateOverallStatus() {
            const results = Object.values(testResults).filter(r => r !== null);
            const passed = results.filter(r => r === true).length;
            const total = results.length;
            
            const container = document.getElementById('overall-results');
            container.innerHTML = `
                <div class="config-display">
Tests Completed: ${total}/6
Tests Passed: ${passed}
Tests Failed: ${total - passed}
Success Rate: ${total > 0 ? Math.round((passed / total) * 100) : 0}%

Status: ${passed === total && total === 6 ? '✅ ALL TESTS PASSED' : 
          total === 0 ? '⏳ NO TESTS RUN' : 
          '❌ SOME TESTS FAILED'}
                </div>
            `;
        }

        // Test functions
        async function testEnvironment() {
            addResult('env-results', 'Testing environment configuration...', 'info');
            
            try {
                // Check for required environment variables
                const envVars = {
                    'VITE_VAPI_PUBLIC_KEY': import.meta.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY,
                    'VITE_VAPI_BASE_URL': import.meta.env?.VITE_VAPI_BASE_URL || window.VITE_VAPI_BASE_URL || 'https://api.vapi.ai',
                    'VITE_VAPI_ASSISTANT_ID': import.meta.env?.VITE_VAPI_ASSISTANT_ID || window.VITE_VAPI_ASSISTANT_ID
                };

                let allGood = true;
                for (const [key, value] of Object.entries(envVars)) {
                    if (!value || value === 'undefined') {
                        addResult('env-results', `❌ Missing: ${key}`, 'error');
                        allGood = false;
                    } else {
                        const displayValue = key.includes('KEY') ? value.substring(0, 8) + '...' : value;
                        addResult('env-results', `✅ Found: ${key} = ${displayValue}`, 'success');
                    }
                }

                updateStatus('environment', allGood);
                addResult('env-results', allGood ? '✅ Environment configuration OK' : '❌ Environment configuration has issues', allGood ? 'success' : 'error');
                
            } catch (error) {
                addResult('env-results', `❌ Environment test failed: ${error.message}`, 'error');
                updateStatus('environment', false);
            }
        }

        async function testAuthentication() {
            addResult('auth-results', 'Testing API authentication...', 'info');
            
            try {
                const apiKey = import.meta.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY;
                if (!apiKey) {
                    throw new Error('No API key found');
                }

                // Test API authentication by fetching assistants
                const response = await fetch('https://api.vapi.ai/assistant', {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    addResult('auth-results', '✅ API authentication successful', 'success');
                    updateStatus('authentication', true);
                } else {
                    throw new Error(`API returned ${response.status}: ${response.statusText}`);
                }
                
            } catch (error) {
                addResult('auth-results', `❌ Authentication failed: ${error.message}`, 'error');
                updateStatus('authentication', false);
            }
        }

        async function testAssistants() {
            addResult('assistant-results', 'Testing assistant management...', 'info');
            
            try {
                const apiKey = import.meta.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY;
                const assistantId = import.meta.env?.VITE_VAPI_ASSISTANT_ID || window.VITE_VAPI_ASSISTANT_ID;
                
                if (!apiKey || !assistantId) {
                    throw new Error('Missing API key or assistant ID');
                }

                // Test fetching specific assistant
                const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const assistant = await response.json();
                    addResult('assistant-results', `✅ Assistant found: ${assistant.name || 'Unnamed'}`, 'success');
                    addResult('assistant-results', `Voice: ${assistant.voice?.provider}/${assistant.voice?.voiceId}`, 'info');
                    addResult('assistant-results', `Model: ${assistant.model?.provider}/${assistant.model?.model}`, 'info');
                    updateStatus('assistants', true);
                } else {
                    throw new Error(`Failed to fetch assistant: ${response.status}`);
                }
                
            } catch (error) {
                addResult('assistant-results', `❌ Assistant test failed: ${error.message}`, 'error');
                updateStatus('assistants', false);
            }
        }

        async function testVoiceConfig() {
            addResult('voice-results', 'Testing voice configuration...', 'info');
            
            try {
                // Check if voice configuration uses OpenAI Echo
                const expectedProvider = 'openai';
                const expectedVoice = 'echo';
                
                // Test default configuration
                const defaultVoice = {
                    provider: import.meta.env?.VITE_VAPI_DEFAULT_VOICE_PROVIDER || 'openai',
                    voiceId: import.meta.env?.VITE_VAPI_DEFAULT_VOICE_ID || 'echo'
                };

                if (defaultVoice.provider === expectedProvider && defaultVoice.voiceId === expectedVoice) {
                    addResult('voice-results', '✅ Voice configuration uses OpenAI Echo', 'success');
                    updateStatus('voice', true);
                } else {
                    addResult('voice-results', `❌ Voice config: ${defaultVoice.provider}/${defaultVoice.voiceId} (expected: ${expectedProvider}/${expectedVoice})`, 'error');
                    updateStatus('voice', false);
                }
                
            } catch (error) {
                addResult('voice-results', `❌ Voice config test failed: ${error.message}`, 'error');
                updateStatus('voice', false);
            }
        }

        async function testWebSDK() {
            addResult('sdk-results', 'Testing Vapi Web SDK...', 'info');
            
            try {
                // Try to load Vapi SDK
                if (typeof window.Vapi === 'undefined') {
                    // Try to load from CDN
                    const script = document.createElement('script');
                    script.src = 'https://cdn.jsdelivr.net/npm/@vapi-ai/web@latest/dist/vapi.js';
                    script.onload = () => {
                        addResult('sdk-results', '✅ Vapi SDK loaded from CDN', 'success');
                        initializeVapi();
                    };
                    script.onerror = () => {
                        addResult('sdk-results', '❌ Failed to load Vapi SDK from CDN', 'error');
                        updateStatus('sdk', false);
                    };
                    document.head.appendChild(script);
                } else {
                    addResult('sdk-results', '✅ Vapi SDK already available', 'success');
                    initializeVapi();
                }
                
            } catch (error) {
                addResult('sdk-results', `❌ SDK test failed: ${error.message}`, 'error');
                updateStatus('sdk', false);
            }
        }

        function initializeVapi() {
            try {
                const apiKey = import.meta.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY;
                if (!apiKey) {
                    throw new Error('No API key for Vapi initialization');
                }

                vapi = new window.Vapi(apiKey);
                addResult('sdk-results', '✅ Vapi instance created successfully', 'success');
                updateStatus('sdk', true);
                
                // Enable call buttons
                document.getElementById('start-call-btn').disabled = false;
                
            } catch (error) {
                addResult('sdk-results', `❌ Vapi initialization failed: ${error.message}`, 'error');
                updateStatus('sdk', false);
            }
        }

        async function startTestCall() {
            if (!vapi) {
                addResult('sdk-results', '❌ Vapi not initialized', 'error');
                return;
            }

            try {
                const assistantId = import.meta.env?.VITE_VAPI_ASSISTANT_ID || window.VITE_VAPI_ASSISTANT_ID;
                if (!assistantId) {
                    throw new Error('No assistant ID configured');
                }

                addResult('sdk-results', 'Starting test call...', 'info');
                await vapi.start(assistantId);
                
                addResult('sdk-results', '✅ Test call started successfully', 'success');
                document.getElementById('start-call-btn').disabled = true;
                document.getElementById('stop-call-btn').disabled = false;
                
            } catch (error) {
                addResult('sdk-results', `❌ Failed to start call: ${error.message}`, 'error');
            }
        }

        async function stopTestCall() {
            if (!vapi) {
                addResult('sdk-results', '❌ Vapi not initialized', 'error');
                return;
            }

            try {
                await vapi.stop();
                addResult('sdk-results', '✅ Test call stopped successfully', 'success');
                document.getElementById('start-call-btn').disabled = false;
                document.getElementById('stop-call-btn').disabled = true;
                
            } catch (error) {
                addResult('sdk-results', `❌ Failed to stop call: ${error.message}`, 'error');
            }
        }

        async function testCallManagement() {
            addResult('call-results', 'Testing call management...', 'info');
            
            try {
                const apiKey = import.meta.env?.VITE_VAPI_PUBLIC_KEY || window.VITE_VAPI_PUBLIC_KEY;
                if (!apiKey) {
                    throw new Error('No API key found');
                }

                // Test fetching recent calls
                const response = await fetch('https://api.vapi.ai/call', {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const calls = await response.json();
                    addResult('call-results', `✅ Retrieved ${calls.length || 0} recent calls`, 'success');
                    updateStatus('calls', true);
                } else {
                    throw new Error(`Failed to fetch calls: ${response.status}`);
                }
                
            } catch (error) {
                addResult('call-results', `❌ Call management test failed: ${error.message}`, 'error');
                updateStatus('calls', false);
            }
        }

        async function runAllTests() {
            addResult('overall-results', 'Running comprehensive test suite...', 'info');
            
            await testEnvironment();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAssistants();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testVoiceConfig();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testWebSDK();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testCallManagement();
            
            updateOverallStatus();
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            updateOverallStatus();
        });
    </script>
</body>
</html>
