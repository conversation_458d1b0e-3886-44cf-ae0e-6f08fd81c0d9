import React, { useState, useEffect } from 'react';
import { DEFAULT_ASSISTANT_ID } from '../constants/vapiConstants';

const VapiStatusPage = () => {
  const [status, setStatus] = useState({
    environment: 'checking',
    authentication: 'checking',
    assistants: 'checking',
    voice: 'checking',
    sdk: 'checking',
    calls: 'checking'
  });

  const [details, setDetails] = useState({});
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    runStatusChecks();
  }, []);

  const runStatusChecks = async () => {
    setIsLoading(true);
    
    // Check environment
    await checkEnvironment();
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Check authentication
    await checkAuthentication();
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Check assistants
    await checkAssistants();
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Check voice configuration
    await checkVoiceConfig();
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Check SDK
    await checkSDK();
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Check calls
    await checkCalls();
    
    setIsLoading(false);
  };

  const checkEnvironment = async () => {
    try {
      const envVars = {
        'VITE_VAPI_PUBLIC_KEY': import.meta.env.VITE_VAPI_PUBLIC_KEY,
        'VITE_VAPI_BASE_URL': import.meta.env.VITE_VAPI_BASE_URL || 'https://api.vapi.ai',
        'VITE_VAPI_ASSISTANT_ID': import.meta.env.VITE_VAPI_ASSISTANT_ID
      };

      const missing = Object.entries(envVars).filter(([key, value]) => !value);
      
      if (missing.length === 0) {
        setStatus(prev => ({ ...prev, environment: 'success' }));
        setDetails(prev => ({ ...prev, environment: 'All required environment variables are set' }));
      } else {
        setStatus(prev => ({ ...prev, environment: 'error' }));
        setDetails(prev => ({ ...prev, environment: `Missing: ${missing.map(([key]) => key).join(', ')}` }));
      }
    } catch (error) {
      setStatus(prev => ({ ...prev, environment: 'error' }));
      setDetails(prev => ({ ...prev, environment: error.message }));
    }
  };

  const checkAuthentication = async () => {
    try {
      const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
      if (!apiKey) {
        throw new Error('No API key found');
      }

      const response = await fetch('https://api.vapi.ai/assistant', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setStatus(prev => ({ ...prev, authentication: 'success' }));
        setDetails(prev => ({ ...prev, authentication: 'API authentication successful' }));
      } else {
        throw new Error(`API returned ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      setStatus(prev => ({ ...prev, authentication: 'error' }));
      setDetails(prev => ({ ...prev, authentication: error.message }));
    }
  };

  const checkAssistants = async () => {
    try {
      const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
      const assistantId = import.meta.env.VITE_VAPI_ASSISTANT_ID || DEFAULT_ASSISTANT_ID;
      
      if (!apiKey || !assistantId) {
        throw new Error('Missing API key or assistant ID');
      }

      const response = await fetch(`https://api.vapi.ai/assistant/${assistantId}`, {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const assistant = await response.json();
        setStatus(prev => ({ ...prev, assistants: 'success' }));
        setDetails(prev => ({ 
          ...prev, 
          assistants: `Assistant "${assistant.name || 'Unnamed'}" found with ${assistant.voice?.provider}/${assistant.voice?.voiceId} voice` 
        }));
      } else {
        throw new Error(`Failed to fetch assistant: ${response.status}`);
      }
    } catch (error) {
      setStatus(prev => ({ ...prev, assistants: 'error' }));
      setDetails(prev => ({ ...prev, assistants: error.message }));
    }
  };

  const checkVoiceConfig = async () => {
    try {
      const expectedProvider = 'openai';
      const expectedVoice = 'echo';
      
      const defaultVoice = {
        provider: import.meta.env.VITE_VAPI_DEFAULT_VOICE_PROVIDER || 'openai',
        voiceId: import.meta.env.VITE_VAPI_DEFAULT_VOICE_ID || 'echo'
      };

      if (defaultVoice.provider === expectedProvider && defaultVoice.voiceId === expectedVoice) {
        setStatus(prev => ({ ...prev, voice: 'success' }));
        setDetails(prev => ({ ...prev, voice: 'Voice configuration uses OpenAI Echo as expected' }));
      } else {
        setStatus(prev => ({ ...prev, voice: 'warning' }));
        setDetails(prev => ({ 
          ...prev, 
          voice: `Voice config: ${defaultVoice.provider}/${defaultVoice.voiceId} (expected: ${expectedProvider}/${expectedVoice})` 
        }));
      }
    } catch (error) {
      setStatus(prev => ({ ...prev, voice: 'error' }));
      setDetails(prev => ({ ...prev, voice: error.message }));
    }
  };

  const checkSDK = async () => {
    try {
      // Check if Vapi SDK is available
      const hasVapi = typeof window.Vapi !== 'undefined';
      
      if (hasVapi) {
        setStatus(prev => ({ ...prev, sdk: 'success' }));
        setDetails(prev => ({ ...prev, sdk: 'Vapi Web SDK is loaded and available' }));
      } else {
        setStatus(prev => ({ ...prev, sdk: 'warning' }));
        setDetails(prev => ({ ...prev, sdk: 'Vapi SDK not loaded - will be loaded dynamically when needed' }));
      }
    } catch (error) {
      setStatus(prev => ({ ...prev, sdk: 'error' }));
      setDetails(prev => ({ ...prev, sdk: error.message }));
    }
  };

  const checkCalls = async () => {
    try {
      const apiKey = import.meta.env.VITE_VAPI_PUBLIC_KEY;
      if (!apiKey) {
        throw new Error('No API key found');
      }

      const response = await fetch('https://api.vapi.ai/call', {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const calls = await response.json();
        setStatus(prev => ({ ...prev, calls: 'success' }));
        setDetails(prev => ({ ...prev, calls: `Call management working - ${calls.length || 0} recent calls found` }));
      } else {
        throw new Error(`Failed to fetch calls: ${response.status}`);
      }
    } catch (error) {
      setStatus(prev => ({ ...prev, calls: 'error' }));
      setDetails(prev => ({ ...prev, calls: error.message }));
    }
  };

  const getStatusIcon = (statusValue) => {
    switch (statusValue) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'checking': return '🔄';
      default: return '❓';
    }
  };

  const getStatusColor = (statusValue) => {
    switch (statusValue) {
      case 'success': return '#28a745';
      case 'warning': return '#ffc107';
      case 'error': return '#dc3545';
      case 'checking': return '#6c757d';
      default: return '#6c757d';
    }
  };

  const overallStatus = () => {
    const statuses = Object.values(status);
    const errors = statuses.filter(s => s === 'error').length;
    const warnings = statuses.filter(s => s === 'warning').length;
    const checking = statuses.filter(s => s === 'checking').length;
    
    if (checking > 0) return 'checking';
    if (errors > 0) return 'error';
    if (warnings > 0) return 'warning';
    return 'success';
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>🎯 Vapi Integration Status</h1>
      
      {/* Overall Status */}
      <div style={{
        background: 'white',
        padding: '20px',
        borderRadius: '8px',
        marginBottom: '20px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        border: `3px solid ${getStatusColor(overallStatus())}`
      }}>
        <h2>{getStatusIcon(overallStatus())} Overall Status</h2>
        <p style={{ fontSize: '18px', margin: 0 }}>
          {overallStatus() === 'success' && 'All systems operational'}
          {overallStatus() === 'warning' && 'Some issues detected but functional'}
          {overallStatus() === 'error' && 'Critical issues need attention'}
          {overallStatus() === 'checking' && 'Running diagnostics...'}
        </p>
        {isLoading && (
          <div style={{ marginTop: '10px' }}>
            <div style={{ 
              width: '100%', 
              height: '4px', 
              background: '#e9ecef', 
              borderRadius: '2px',
              overflow: 'hidden'
            }}>
              <div style={{
                width: '30%',
                height: '100%',
                background: '#007bff',
                animation: 'loading 2s infinite'
              }}></div>
            </div>
          </div>
        )}
      </div>

      {/* Individual Status Checks */}
      <div style={{ display: 'grid', gap: '15px' }}>
        {[
          { key: 'environment', title: 'Environment Configuration' },
          { key: 'authentication', title: 'API Authentication' },
          { key: 'assistants', title: 'Assistant Management' },
          { key: 'voice', title: 'Voice Configuration' },
          { key: 'sdk', title: 'Web SDK Integration' },
          { key: 'calls', title: 'Call Management' }
        ].map(({ key, title }) => (
          <div key={key} style={{
            background: 'white',
            padding: '15px',
            borderRadius: '8px',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
            borderLeft: `4px solid ${getStatusColor(status[key])}`
          }}>
            <h3 style={{ margin: '0 0 10px 0', display: 'flex', alignItems: 'center' }}>
              <span style={{ marginRight: '10px' }}>{getStatusIcon(status[key])}</span>
              {title}
            </h3>
            <p style={{ margin: 0, color: '#6c757d' }}>
              {details[key] || 'Checking...'}
            </p>
          </div>
        ))}
      </div>

      {/* Actions */}
      <div style={{ marginTop: '20px', textAlign: 'center' }}>
        <button
          onClick={runStatusChecks}
          disabled={isLoading}
          style={{
            background: '#007bff',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '6px',
            cursor: isLoading ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            opacity: isLoading ? 0.6 : 1
          }}
        >
          {isLoading ? '🔄 Checking...' : '🔄 Refresh Status'}
        </button>
      </div>

      <style jsx>{`
        @keyframes loading {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(400%); }
        }
      `}</style>
    </div>
  );
};

export default VapiStatusPage;
