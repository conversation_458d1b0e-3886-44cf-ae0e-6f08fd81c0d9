/**
 * Vapi Configuration Service
 *
 * A service for fetching and caching Vapi configuration,
 * including available voice providers and models.
 */

// Cache for Vapi configuration
let configCache = null;
let lastFetchTime = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Fetch Vapi configuration from the server
 * @returns {Promise<Object>} The Vapi configuration
 */
export async function fetchVapiConfig() {
  try {
    // Check if we have a cached config that's still valid
    const now = Date.now();
    if (configCache && (now - lastFetchTime < CACHE_TTL)) {
      console.log('[VapiConfigService] Using cached Vapi configuration');
      return configCache;
    }

    console.log('[VapiConfigService] Fetching Vapi configuration');

    // Fetch available assistants to get voice providers
    const response = await fetch('/api/vapi/config');

    if (!response.ok) {
      throw new Error(`Failed to fetch Vapi configuration: ${response.status} ${response.statusText}`);
    }

    const config = await response.json();

    // Cache the config
    configCache = config;
    lastFetchTime = now;

    console.log('[VapiConfigService] Vapi configuration fetched successfully:', config);

    return config;
  } catch (error) {
    console.error('[VapiConfigService] Error fetching Vapi configuration:', error);

    // If we have a cached config, return it even if it's expired
    if (configCache) {
      console.log('[VapiConfigService] Falling back to cached Vapi configuration');
      return configCache;
    }

    // Otherwise, return a default configuration
    return getDefaultVapiConfig();
  }
}

/**
 * Get default Vapi configuration
 * @returns {Object} The default Vapi configuration
 */
export function getDefaultVapiConfig() {
  return {
    voiceProviders: [
      {
        id: 'openai',
        name: 'OpenAI',
        voices: [
          { id: 'echo', name: 'Echo' },
          { id: 'alloy', name: 'Alloy' },
          { id: 'nova', name: 'Nova' },
          { id: 'EXAVITQu4vr4xnSDxMaL', name: 'Bella' }
        ]
      },
      {
        id: 'openai',
        name: 'OpenAI',
        voices: [
          { id: 'alloy', name: 'Alloy' },
          { id: 'echo', name: 'Echo' },
          { id: 'fable', name: 'Fable' },
          { id: 'onyx', name: 'Onyx' },
          { id: 'nova', name: 'Nova' },
          { id: 'shimmer', name: 'Shimmer' }
        ]
      }
    ],
    llmProviders: [
      {
        id: 'openai',
        name: 'OpenAI',
        models: [
          { id: 'gpt-4o', name: 'GPT-4o' },
          { id: 'gpt-4o-mini', name: 'GPT-4o Mini' }
        ]
      },
      {
        id: 'anthropic',
        name: 'Anthropic',
        models: [
          { id: 'claude-3-7-sonnet-20250219', name: 'Claude 3.7 Sonnet' },
          { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku' }
        ]
      }
    ],
    defaultVoice: {
      provider: 'openai',
      voiceId: 'echo' // OpenAI Echo voice - reliable and high quality
    },
    defaultLlm: {
      provider: 'openai',
      model: 'gpt-4o'
    }
  };
}

/**
 * Get available voice providers
 * @returns {Promise<Array>} The available voice providers
 */
export async function getVoiceProviders() {
  const config = await fetchVapiConfig();
  return config.voiceProviders || [];
}

/**
 * Get available voices for a provider
 * @param {string} providerId - The provider ID
 * @returns {Promise<Array>} The available voices
 */
export async function getVoicesForProvider(providerId) {
  const providers = await getVoiceProviders();
  const provider = providers.find(p => p.id === providerId);

  if (!provider) {
    console.warn(`[VapiConfigService] Voice provider not found: ${providerId}`);
    return [];
  }

  return provider.voices || [];
}

/**
 * Get default voice configuration
 * @returns {Promise<Object>} The default voice configuration
 */
export async function getDefaultVoice() {
  const config = await fetchVapiConfig();
  return config.defaultVoice || { provider: 'openai', voiceId: 'echo' };
}

/**
 * Get default LLM configuration
 * @returns {Promise<Object>} The default LLM configuration
 */
export async function getDefaultLlm() {
  const config = await fetchVapiConfig();
  return config.defaultLlm || { provider: 'openai', model: 'gpt-4o' };
}

/**
 * Create a Vapi assistant with the given configuration
 * @param {Object} config - The assistant configuration
 * @returns {Promise<Object>} The created assistant
 */
export async function createVapiAssistant(config) {
  try {
    console.log('[VapiConfigService] Creating Vapi assistant:', config);

    // Get default voice and LLM if not provided
    const defaultVoice = await getDefaultVoice();
    const defaultLlm = await getDefaultLlm();

    // Merge with defaults
    const assistantConfig = {
      name: config.name || 'Legal Assistant',
      firstMessage: config.firstMessage || 'Hello, how can I help you today?',
      instructions: config.instructions || 'You are a legal assistant helping potential clients.',
      voice: config.voice || defaultVoice,
      llm: config.llm || defaultLlm
    };

    // Create the assistant
    const response = await fetch('/api/vapi/assistants', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(assistantConfig)
    });

    if (!response.ok) {
      throw new Error(`Failed to create Vapi assistant: ${response.status} ${response.statusText}`);
    }

    const assistant = await response.json();

    console.log('[VapiConfigService] Vapi assistant created successfully:', assistant);

    return assistant;
  } catch (error) {
    console.error('[VapiConfigService] Error creating Vapi assistant:', error);
    throw error;
  }
}

/**
 * Update a Vapi assistant with the given configuration
 * @param {string} assistantId - The assistant ID
 * @param {Object} config - The assistant configuration
 * @returns {Promise<Object>} The updated assistant
 */
export async function updateVapiAssistant(assistantId, config) {
  try {
    console.log('[VapiConfigService] Updating Vapi assistant:', assistantId, config);

    // Update the assistant
    const response = await fetch(`/api/vapi/assistants/${assistantId}`, {
      method: 'PATCH',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(config)
    });

    if (!response.ok) {
      throw new Error(`Failed to update Vapi assistant: ${response.status} ${response.statusText}`);
    }

    const assistant = await response.json();

    console.log('[VapiConfigService] Vapi assistant updated successfully:', assistant);

    return assistant;
  } catch (error) {
    console.error('[VapiConfigService] Error updating Vapi assistant:', error);
    throw error;
  }
}

/**
 * Get a Vapi assistant by ID
 * @param {string} assistantId - The assistant ID
 * @returns {Promise<Object>} The assistant
 */
export async function getVapiAssistant(assistantId) {
  try {
    console.log('[VapiConfigService] Getting Vapi assistant:', assistantId);

    // Get the assistant
    const response = await fetch(`/api/vapi/assistants/${assistantId}`);

    if (!response.ok) {
      throw new Error(`Failed to get Vapi assistant: ${response.status} ${response.statusText}`);
    }

    const assistant = await response.json();

    console.log('[VapiConfigService] Vapi assistant fetched successfully:', assistant);

    return assistant;
  } catch (error) {
    console.error('[VapiConfigService] Error getting Vapi assistant:', error);
    throw error;
  }
}

// Export a default object for convenience
export default {
  fetchVapiConfig,
  getDefaultVapiConfig,
  getVoiceProviders,
  getVoicesForProvider,
  getDefaultVoice,
  getDefaultLlm,
  createVapiAssistant,
  updateVapiAssistant,
  getVapiAssistant
};
